export interface TestScenario {
  id: string;
  name: string;
  description: string;
  url: string;
  steps: TestStep[];
  assertions: Assertion[];
  status: 'pending' | 'running' | 'passed' | 'failed';
  createdAt: Date | string;
  updatedAt: Date | string;
}

export interface TestStep {
  id: string;
  type: 'navigate' | 'click' | 'type' | 'wait' | 'scroll' | 'screenshot' | 'extract';
  selector?: string;
  value?: string;
  timeout?: number;
  description: string;
}

export interface Assertion {
  id: string;
  type: 'exists' | 'not_exists' | 'contains_text' | 'equals' | 'visible' | 'not_visible';
  selector?: string;
  expectedValue?: string;
  description: string;
}

export interface TestResult {
  scenarioId: string;
  status: 'passed' | 'failed';
  executionTime: number;
  stepResults: StepResult[];
  errors: string[];
  screenshots: string[];
  timestamp: Date | string;
}

export interface StepResult {
  stepId: string;
  status: 'passed' | 'failed' | 'skipped';
  executionTime: number;
  error?: string;
  screenshot?: string;
}

export interface Agent {
  id: string;
  name: string;
  type: 'planner' | 'executor' | 'validator' | 'reporter';
  status: 'idle' | 'active' | 'error';
  capabilities: string[];
}

export interface ExecutionPlan {
  id: string;
  scenarioId: string;
  steps: PlannedStep[];
  estimatedDuration: number;
  agents: Agent[];
}

export interface PlannedStep {
  step: TestStep;
  assignedAgent: string;
  dependencies: string[];
  estimatedDuration: number;
}

export interface WebElementInfo {
  selector: string;
  tagName: string;
  text: string;
  attributes: Record<string, string>;
  isVisible: boolean;
  boundingBox: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
}
