import { generateObject } from 'ai';
import { createOpenRouter } from '@openrouter/ai-sdk-provider';
import { z } from 'zod';
import { TestResult, TestScenario } from '@/types/automation';

const reportSchema = z.object({
  summary: z.object({
    totalTests: z.number(),
    passed: z.number(),
    failed: z.number(),
    successRate: z.number(),
    totalExecutionTime: z.number()
  }),
  insights: z.array(z.object({
    type: z.enum(['performance', 'reliability', 'usability', 'bug']),
    message: z.string(),
    severity: z.enum(['low', 'medium', 'high', 'critical']),
    recommendation: z.string()
  })),
  recommendations: z.array(z.string()),
  riskAssessment: z.string()
});

interface ReportSummary {
  totalTests: number;
  passed: number;
  failed: number;
  successRate: number;
  totalExecutionTime: number;
}

interface ReportInsight {
  type: 'performance' | 'reliability' | 'usability' | 'bug';
  message: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  recommendation: string;
}

interface ReportData {
  summary: ReportSummary;
  insights: ReportInsight[];
  recommendations: string[];
  riskAssessment: string;
}

export class ReporterAgent {
  private openrouter = createOpenRouter({
    apiKey: process.env.OPENROUTER_API_KEY!,
  });
  private model = this.openrouter(process.env.OPENROUTER_MODEL_ID!);

  async generateReport(
    scenarios: TestScenario[],
    results: TestResult[]
  ): Promise<ReportData> {
    const prompt = `
You are a test automation reporter. Generate a comprehensive test report based on the following data:

Test Scenarios:
${scenarios.map((scenario, index) => `
${index + 1}. ${scenario.name}
   Description: ${scenario.description}
   Status: ${scenario.status}
   Steps: ${scenario.steps.length}
   Assertions: ${scenario.assertions.length}
`).join('\n')}

Test Results:
${results.map((result, index) => `
${index + 1}. Scenario ID: ${result.scenarioId}
   Status: ${result.status}
   Execution Time: ${result.executionTime}ms
   Errors: ${result.errors.length}
   Step Results: ${result.stepResults.map(sr => `${sr.status} (${sr.executionTime}ms)`).join(', ')}
`).join('\n')}

Generate a report that includes:
1. Summary statistics
2. Performance insights
3. Reliability patterns
4. Usability observations
5. Bug detection
6. Recommendations for improvement
7. Risk assessment

Focus on actionable insights and specific recommendations.
`;

    try {
      const result = await generateObject({
        model: this.model,
        prompt,
        schema: reportSchema,
      });

      return result.object;
    } catch (error) {
      console.error('Error generating report:', error);
      throw new Error('Failed to generate test report');
    }
  }

  async analyzePerformance(results: TestResult[]): Promise<{
    averageExecutionTime: number;
    slowestSteps: Array<{ stepId: string; executionTime: number }>;
    performanceBottlenecks: string[];
  }> {
    const allStepResults = results.flatMap(r => r.stepResults);
    const totalTime = allStepResults.reduce((sum, sr) => sum + sr.executionTime, 0);
    const averageExecutionTime = totalTime / allStepResults.length;

    const slowestSteps = allStepResults
      .filter(sr => sr.executionTime > averageExecutionTime * 1.5)
      .sort((a, b) => b.executionTime - a.executionTime)
      .slice(0, 5)
      .map(sr => ({ stepId: sr.stepId, executionTime: sr.executionTime }));

    const performanceBottlenecks = await this.identifyBottlenecks(results);

    return {
      averageExecutionTime,
      slowestSteps,
      performanceBottlenecks
    };
  }

  private async identifyBottlenecks(results: TestResult[]): Promise<string[]> {
    const prompt = `
Analyze the following test execution data and identify performance bottlenecks:

${results.map(r => `
Scenario: ${r.scenarioId}
Total Time: ${r.executionTime}ms
Step Times: ${r.stepResults.map(sr => `${sr.stepId}: ${sr.executionTime}ms`).join(', ')}
Errors: ${r.errors.join('; ')}
`).join('\n')}

Identify specific bottlenecks and patterns that could be optimized.
`;

    try {
      const result = await generateObject({
        model: this.model,
        prompt,
        schema: z.object({
          bottlenecks: z.array(z.string())
        }),
      });

      return result.object.bottlenecks;
    } catch (error) {
      console.error('Error identifying bottlenecks:', error);
      return ['Unable to analyze bottlenecks due to processing error'];
    }
  }

  async generateFailureAnalysis(failedResults: TestResult[]): Promise<{
    commonFailurePatterns: string[];
    rootCauses: string[];
    suggestedFixes: string[];
  }> {
    if (failedResults.length === 0) {
      return {
        commonFailurePatterns: [],
        rootCauses: [],
        suggestedFixes: []
      };
    }

    const prompt = `
Analyze the following failed test results and identify patterns:

${failedResults.map(r => `
Scenario: ${r.scenarioId}
Errors: ${r.errors.join('; ')}
Failed Steps: ${r.stepResults.filter(sr => sr.status === 'failed').map(sr => `${sr.stepId}: ${sr.error}`).join('; ')}
`).join('\n')}

Identify:
1. Common failure patterns
2. Root causes
3. Suggested fixes

Focus on actionable insights that can prevent future failures.
`;

    try {
      const result = await generateObject({
        model: this.model,
        prompt,
        schema: z.object({
          commonFailurePatterns: z.array(z.string()),
          rootCauses: z.array(z.string()),
          suggestedFixes: z.array(z.string())
        }),
      });

      return result.object;
    } catch (error) {
      console.error('Error analyzing failures:', error);
      return {
        commonFailurePatterns: ['Unable to analyze failure patterns'],
        rootCauses: ['Analysis error occurred'],
        suggestedFixes: ['Review test configuration and retry']
      };
    }
  }

  async takeScreenshot(): Promise<string> {
    // In a real implementation, this would capture actual screenshots
    console.log('Taking screenshot for report');
    return 'data:image/png;base64,fake_screenshot_data';
  }

  async exportReport(reportData: ReportData, format: 'html' | 'pdf' | 'json'): Promise<string> {
    // In a real implementation, this would generate actual report files
    console.log(`Exporting report in ${format} format`);
    return `report_${Date.now()}.${format}`;
  }
}
