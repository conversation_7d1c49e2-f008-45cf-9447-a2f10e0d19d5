import { NextRequest, NextResponse } from 'next/server';
import { generateObject } from 'ai';
import { createOpenAI } from '@ai-sdk/openai';
import { z } from 'zod';
import { TestScenario, TestStep, Assertion } from '@/types/automation';

const testScenarioSchema = z.object({
  name: z.string(),
  description: z.string(),
  steps: z.array(z.object({
    type: z.enum(['navigate', 'click', 'type', 'wait', 'scroll', 'screenshot', 'extract']),
    selector: z.string().optional(),
    value: z.string().optional(),
    timeout: z.number().optional(),
    description: z.string()
  })),
  assertions: z.array(z.object({
    type: z.enum(['exists', 'not_exists', 'contains_text', 'equals', 'visible', 'not_visible']),
    selector: z.string().optional(),
    expectedValue: z.string().optional(),
    description: z.string()
  }))
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { description, url } = body as { description: string; url: string };

    if (!description || !url) {
      return NextResponse.json(
        { error: 'Description and URL are required' },
        { status: 400 }
      );
    }

    const prompt = `
Generate a comprehensive web automation test scenario based on the following description:

Description: ${description}
Target URL: ${url}

Create a test scenario that includes:
1. A clear name and description
2. Detailed steps to perform the test (navigate, click, type, wait, etc.)
3. Appropriate assertions to verify the expected behavior

Guidelines:
- Use specific CSS selectors where possible (e.g., #login-button, .submit-form, input[name="email"])
- Include realistic timeouts for wait operations (1000-5000ms)
- Add assertions to verify each critical step
- Break down complex interactions into smaller steps
- Include screenshots at key points for documentation

Focus on creating a robust, maintainable test that covers the core functionality described.
`;

    const ollama = createOpenAI({
      baseURL: process.env.OLLAMA_BASE_URL + '/v1',
      apiKey: 'not-needed' // Ollama doesn't require API key
    });

    const result = await generateObject({
      model: ollama(process.env.OLLAMA_MODEL_ID!),
      prompt,
      schema: testScenarioSchema,
    });

    // Convert the generated data to our internal types
    const steps: TestStep[] = result.object.steps.map((step, index) => ({
      id: `step_${Date.now()}_${index}`,
      type: step.type,
      selector: step.selector,
      value: step.value,
      timeout: step.timeout,
      description: step.description
    }));

    const assertions: Assertion[] = result.object.assertions.map((assertion, index) => ({
      id: `assertion_${Date.now()}_${index}`,
      type: assertion.type,
      selector: assertion.selector,
      expectedValue: assertion.expectedValue,
      description: assertion.description
    }));

    const testScenario: TestScenario = {
      id: `scenario_${Date.now()}`,
      name: result.object.name,
      description: result.object.description,
      url,
      steps,
      assertions,
      status: 'pending',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    return NextResponse.json({
      success: true,
      scenario: testScenario
    });

  } catch (error) {
    console.error('Error generating test scenario:', error);
    return NextResponse.json(
      {
        error: 'Failed to generate test scenario',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Test scenario generation endpoint',
    usage: 'POST with description and URL'
  });
}
