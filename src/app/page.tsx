'use client';

import { useState } from 'react';
import { TestScenario, TestResult, Agent } from '@/types/automation';
import { ScenarioBuilder } from '@/components/scenario-builder';
import { AgentDashboard } from '@/components/agent-dashboard';
import { TestResults } from '@/components/test-results';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';

export default function Home() {
  const [scenarios, setScenarios] = useState<TestScenario[]>([]);
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [agents] = useState<Agent[]>([
    {
      id: 'planner',
      name: 'Planner Agent',
      type: 'planner',
      status: 'idle',
      capabilities: ['analyze', 'plan', 'optimize']
    },
    {
      id: 'executor',
      name: 'Executor Agent',
      type: 'executor',
      status: 'idle',
      capabilities: ['navigate', 'click', 'type', 'wait', 'scroll']
    },
    {
      id: 'validator',
      name: 'Validator Agent',
      type: 'validator',
      status: 'idle',
      capabilities: ['assert', 'verify', 'validate']
    },
    {
      id: 'reporter',
      name: 'Reporter Agent',
      type: 'reporter',
      status: 'idle',
      capabilities: ['report', 'analyze', 'screenshot']
    }
  ]);
  const [isExecuting, setIsExecuting] = useState(false);

  const handleScenarioCreate = (scenario: TestScenario) => {
    setScenarios(prev => [...prev, scenario]);
  };

  const handleScenarioExecute = async (scenario: TestScenario) => {
    setIsExecuting(true);
    
    try {
      const response = await fetch('/api/test/execute', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ scenario }),
      });

      if (!response.ok) {
        throw new Error('Failed to execute test scenario');
      }

      const data = await response.json();
      setTestResults(prev => [...prev, data.result]);
      
      // Update scenario status
      setScenarios(prev => 
        prev.map(s => 
          s.id === scenario.id 
            ? { ...s, status: data.result.status }
            : s
        )
      );
    } catch (error) {
      console.error('Error executing scenario:', error);
    } finally {
      setIsExecuting(false);
    }
  };

  const passedTests = testResults.filter(r => r.status === 'passed').length;
  const failedTests = testResults.filter(r => r.status === 'failed').length;
  const totalTests = testResults.length;
  const successRate = totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 0;

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-2">
            AI Web Automation Testing Platform
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300">
            Multi-agent system for intelligent web application testing
          </p>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Scenarios</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{scenarios.length}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Tests Executed</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalTests}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{successRate}%</div>
              <div className="flex gap-1 mt-2">
                <Badge variant={passedTests > 0 ? "default" : "secondary"}>
                  {passedTests} passed
                </Badge>
                <Badge variant={failedTests > 0 ? "destructive" : "secondary"}>
                  {failedTests} failed
                </Badge>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Agents</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {agents.filter(a => a.status === 'active').length}
              </div>
              <div className="text-xs text-muted-foreground">
                of {agents.length} agents
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs defaultValue="scenarios" className="space-y-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="scenarios">Test Scenarios</TabsTrigger>
            <TabsTrigger value="agents">Agent Dashboard</TabsTrigger>
            <TabsTrigger value="results">Test Results</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          <TabsContent value="scenarios" className="space-y-4">
            <ScenarioBuilder 
              onScenarioCreate={handleScenarioCreate}
              onScenarioExecute={handleScenarioExecute}
              scenarios={scenarios}
              isExecuting={isExecuting}
            />
          </TabsContent>

          <TabsContent value="agents" className="space-y-4">
            <AgentDashboard agents={agents} />
          </TabsContent>

          <TabsContent value="results" className="space-y-4">
            <TestResults results={testResults} scenarios={scenarios} />
          </TabsContent>

          <TabsContent value="analytics" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Test Analytics</CardTitle>
                <CardDescription>
                  Performance insights and trends analysis
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-muted-foreground">
                  Analytics dashboard coming soon...
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
