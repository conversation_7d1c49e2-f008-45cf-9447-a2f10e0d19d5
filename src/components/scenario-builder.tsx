'use client';

import { useState } from 'react';
import { TestScenario } from '@/types/automation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Play, Plus, Loader2, Brain, Eye } from 'lucide-react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';

interface ScenarioBuilderProps {
  onScenarioCreate: (scenario: TestScenario) => void;
  onScenarioExecute: (scenario: TestScenario) => void;
  scenarios: TestScenario[];
  isExecuting: boolean;
}

export function ScenarioBuilder({ 
  onScenarioCreate, 
  onScenarioExecute, 
  scenarios, 
  isExecuting 
}: ScenarioBuilderProps) {
  const [isCreating, setIsCreating] = useState(false);
  const [description, setDescription] = useState('');
  const [url, setUrl] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);

  const handleGenerateScenario = async () => {
    if (!description.trim() || !url.trim()) return;

    setIsGenerating(true);
    try {
      const response = await fetch('/api/test/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ description, url }),
      });

      if (!response.ok) {
        throw new Error('Failed to generate scenario');
      }

      const data = await response.json();
      onScenarioCreate(data.scenario);
      setDescription('');
      setUrl('');
      setIsCreating(false);
    } catch (error) {
      console.error('Error generating scenario:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const getStatusColor = (status: TestScenario['status']) => {
    switch (status) {
      case 'passed': return 'bg-green-500';
      case 'failed': return 'bg-red-500';
      case 'running': return 'bg-yellow-500';
      default: return 'bg-gray-400';
    }
  };

  const getStatusVariant = (status: TestScenario['status']) => {
    switch (status) {
      case 'passed': return 'default';
      case 'failed': return 'destructive';
      case 'running': return 'secondary';
      default: return 'outline';
    }
  };

  return (
    <div className="space-y-6">
      {/* Create New Scenario */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            AI-Powered Test Scenario Generator
          </CardTitle>
          <CardDescription>
            Describe what you want to test, and our AI will generate a comprehensive test scenario
          </CardDescription>
        </CardHeader>
        <CardContent>
          {!isCreating ? (
            <Button onClick={() => setIsCreating(true)} className="w-full">
              <Plus className="h-4 w-4 mr-2" />
              Create New Test Scenario
            </Button>
          ) : (
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium mb-2 block">
                  Test Description
                </label>
                <Textarea
                  placeholder="Describe what you want to test (e.g., 'Test user login with valid credentials')"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  rows={3}
                />
              </div>
              <div>
                <label className="text-sm font-medium mb-2 block">
                  Target URL
                </label>
                <Input
                  placeholder="https://example.com"
                  value={url}
                  onChange={(e) => setUrl(e.target.value)}
                />
              </div>
              <div className="flex gap-2">
                <Button
                  onClick={handleGenerateScenario}
                  disabled={!description.trim() || !url.trim() || isGenerating}
                  className="flex-1"
                >
                  {isGenerating ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Brain className="h-4 w-4 mr-2" />
                      Generate Test Scenario
                    </>
                  )}
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    setIsCreating(false);
                    setDescription('');
                    setUrl('');
                  }}
                >
                  Cancel
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Scenarios List */}
      <div className="grid gap-4">
        {scenarios.length > 0 ? (
          scenarios.map((scenario) => (
            <Card key={scenario.id}>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg">{scenario.name}</CardTitle>
                    <CardDescription className="mt-2">
                      {scenario.description}
                    </CardDescription>
                    <div className="flex items-center gap-2 mt-3">
                      <span className="text-sm text-muted-foreground">
                        URL: {scenario.url}
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant={getStatusVariant(scenario.status)}>
                      <div className={`w-2 h-2 rounded-full ${getStatusColor(scenario.status)} mr-2`} />
                      {scenario.status}
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div className="flex gap-4 text-sm text-muted-foreground">
                    <span>{scenario.steps.length} steps</span>
                    <span>{scenario.assertions.length} assertions</span>
                    <span>Created {new Date(scenario.createdAt).toLocaleDateString()}</span>
                  </div>
                  <div className="flex gap-2">
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4 mr-2" />
                          View Details
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                        <DialogHeader>
                          <DialogTitle>{scenario.name}</DialogTitle>
                          <DialogDescription>
                            {scenario.description}
                          </DialogDescription>
                        </DialogHeader>
                        <div className="space-y-4">
                          <div>
                            <h4 className="font-medium mb-2">Test Steps:</h4>
                            <div className="space-y-2">
                              {scenario.steps.map((step, index) => (
                                <div key={step.id} className="flex gap-3 p-3 bg-gray-50 rounded-lg">
                                  <div className="bg-blue-100 text-blue-800 rounded-full w-6 h-6 flex items-center justify-center text-xs font-medium">
                                    {index + 1}
                                  </div>
                                  <div className="flex-1">
                                    <div className="font-medium">{step.type.toUpperCase()}</div>
                                    <div className="text-sm text-gray-600">{step.description}</div>
                                    {step.selector && (
                                      <div className="text-xs text-gray-500 mt-1">
                                        Selector: {step.selector}
                                      </div>
                                    )}
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                          <div>
                            <h4 className="font-medium mb-2">Assertions:</h4>
                            <div className="space-y-2">
                              {scenario.assertions.map((assertion) => (
                                <div key={assertion.id} className="flex gap-3 p-3 bg-green-50 rounded-lg">
                                  <div className="bg-green-100 text-green-800 rounded-full w-6 h-6 flex items-center justify-center text-xs font-medium">
                                    ✓
                                  </div>
                                  <div className="flex-1">
                                    <div className="font-medium">{assertion.type.toUpperCase()}</div>
                                    <div className="text-sm text-gray-600">{assertion.description}</div>
                                    {assertion.expectedValue && (
                                      <div className="text-xs text-gray-500 mt-1">
                                        Expected: {assertion.expectedValue}
                                      </div>
                                    )}
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                      </DialogContent>
                    </Dialog>
                    <Button
                      onClick={() => onScenarioExecute(scenario)}
                      disabled={isExecuting || scenario.status === 'running'}
                      size="sm"
                    >
                      {scenario.status === 'running' ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Running...
                        </>
                      ) : (
                        <>
                          <Play className="h-4 w-4 mr-2" />
                          Execute
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        ) : (
          <Card>
            <CardContent className="text-center py-8">
              <div className="text-muted-foreground">
                No test scenarios created yet. Generate your first test scenario using AI!
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
