import { FC } from 'react';
import { Agent } from '@/types/automation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Settings, Activity } from 'lucide-react';

interface AgentDashboardProps {
  agents: Agent[];
}

export const AgentDashboard: FC<AgentDashboardProps> = ({ agents }) => {
  return (
    <div className="grid md:grid-cols-2 gap-4">
      {agents.map((agent) => (
        <Card key={agent.id}>
          <CardHeader className="flex items-start justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                {agent.type === 'planner' && <Settings className="h-4 w-4" />}
                {agent.type === 'executor' && <Activity className="h-4 w-4" />}
                {agent.type === 'validator' && <Settings className="h-4 w-4" />}
                {agent.type === 'reporter' && <Activity className="h-4 w-4" />}
                {agent.name}
              </CardTitle>
            </div>
            <Badge variant={agent.status === 'active' ? 'default' : 'secondary'}>
              {agent.status}
            </Badge>
          </CardHeader>
          <CardContent>
            <div className="text-sm text-muted-foreground">
              Capabilities:
            </div>
            <ul className="list-disc ml-4">
              {agent.capabilities.map((capability, index) => (
                <li key={index} className="text-sm">
                  {capability}
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

