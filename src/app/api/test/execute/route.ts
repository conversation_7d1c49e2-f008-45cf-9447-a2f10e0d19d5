import { NextRequest, NextResponse } from 'next/server';
import { TestOrchestrator } from '@/lib/orchestrator';
import { TestScenario, TestStep, Assertion } from '@/types/automation';

// Default test scenario for Turkish Airlines
const createDefaultTestScenario = (): TestScenario => {
  const defaultSteps: TestStep[] = [
    {
      id: `step_${Date.now()}_1`,
      type: 'navigate',
      description: 'Navigate to Turkish Airlines homepage',
      timeout: 5000
    },
    {
      id: `step_${Date.now()}_2`,
      type: 'wait',
      description: 'Wait for page to load',
      timeout: 3000
    },
    {
      id: `step_${Date.now()}_3`,
      type: 'screenshot',
      description: 'Take screenshot of homepage'
    },
    {
      id: `step_${Date.now()}_4`,
      type: 'click',
      selector: '[data-testid="search-button"], .search-button, button[type="submit"]',
      description: 'Click search or main action button',
      timeout: 2000
    }
  ];

  const defaultAssertions: Assertion[] = [
    {
      id: `assertion_${Date.now()}_1`,
      type: 'exists',
      selector: 'title, h1, .logo',
      description: 'Verify page has loaded with main elements'
    },
    {
      id: `assertion_${Date.now()}_2`,
      type: 'visible',
      selector: 'body',
      description: 'Verify page body is visible'
    }
  ];

  return {
    id: `scenario_${Date.now()}`,
    name: 'Turkish Airlines Homepage Test',
    description: 'Test basic functionality of Turkish Airlines website - navigate to homepage, verify page loads correctly, and test main search functionality',
    url: 'https://www.turkishairlines.com',
    steps: defaultSteps,
    assertions: defaultAssertions,
    status: 'pending',
    createdAt: new Date(),
    updatedAt: new Date()
  };
};

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    let { scenario } = body as { scenario?: TestScenario };

    // If no scenario is provided, use the default Turkish Airlines test scenario
    if (!scenario) {
      scenario = createDefaultTestScenario();
      console.log('No scenario provided, using default Turkish Airlines test scenario');
    }

    // Ensure required fields have defaults if missing
    if (!scenario.url) {
      scenario.url = 'https://www.turkishairlines.com';
    }

    if (!scenario.description || scenario.description.trim() === '') {
      scenario.description = 'Test basic functionality of Turkish Airlines website - navigate to homepage, verify page loads correctly, and test main search functionality';
    }

    const orchestrator = new TestOrchestrator();
    const result = await orchestrator.executeScenario(scenario);

    return NextResponse.json({
      success: true,
      result,
      usedDefaultScenario: !body.scenario // Indicate if default was used
    });

  } catch (error) {
    console.error('Error executing test scenario:', error);
    return NextResponse.json(
      {
        error: 'Failed to execute test scenario',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  const defaultScenario = createDefaultTestScenario();

  return NextResponse.json({
    message: 'Test execution endpoint',
    usage: 'POST with test scenario data. If no scenario is provided, a default Turkish Airlines test will be executed.',
    defaultScenario: {
      name: defaultScenario.name,
      description: defaultScenario.description,
      url: defaultScenario.url,
      stepsCount: defaultScenario.steps.length,
      assertionsCount: defaultScenario.assertions.length
    },
    examples: {
      withScenario: {
        scenario: {
          id: 'custom_scenario_id',
          name: 'Custom Test',
          description: 'Custom test description',
          url: 'https://example.com',
          steps: [],
          assertions: [],
          status: 'pending',
          createdAt: new Date(),
          updatedAt: new Date()
        }
      },
      withoutScenario: 'POST with empty body {} will use default Turkish Airlines scenario'
    }
  });
}
