import { generateObject } from 'ai';
import { createOpenAI } from '@ai-sdk/openai';
import { z } from 'zod';
import { TestScenario, ExecutionPlan, PlannedStep } from '@/types/automation';

const executionPlanSchema = z.object({
  steps: z.array(z.object({
    stepId: z.string(),
    assignedAgent: z.string(),
    dependencies: z.array(z.string()),
    estimatedDuration: z.number(),
    reasoning: z.string()
  })),
  estimatedTotalDuration: z.number(),
  riskAssessment: z.string(),
  optimizations: z.array(z.string())
});

export class PlannerAgent {
  private ollama = createOpenAI({
    baseURL: process.env.OLLAMA_BASE_URL + '/v1',
    apiKey: 'not-needed'
  });
  private model = this.ollama(process.env.OLLAMA_MODEL_ID!);

  async createExecutionPlan(scenario: TestScenario): Promise<ExecutionPlan> {
    const prompt = `
You are a test automation planner. Create an optimized execution plan for the following test scenario:

Scenario: ${scenario.name}
Description: ${scenario.description}
URL: ${scenario.url}

Steps (use these exact IDs in your response):
${scenario.steps.map((step, index) => 
  `${index + 1}. ID: ${step.id} | ${step.type}: ${step.description} ${step.selector ? `(selector: ${step.selector})` : ''}`
).join('\n')}

Assertions (for context only - these will be handled separately):
${scenario.assertions.map((assertion, index) => 
  `${index + 1}. ${assertion.type}: ${assertion.description} ${assertion.selector ? `(selector: ${assertion.selector})` : ''}`
).join('\n')}

Available agents:
- executor: Can perform web actions (click, type, navigate)
- validator: Can verify assertions and element states
- reporter: Can take screenshots and extract information

Create an execution plan that:
1. Uses the EXACT step IDs provided above (e.g., step_1722278400000_0)
2. Assigns each step to the most appropriate agent
3. Identifies dependencies between steps (use step IDs for dependencies)
4. Estimates execution time for each step (in milliseconds)
5. Optimizes for parallel execution where possible
6. Includes risk assessment and mitigation strategies

IMPORTANT: You must use the exact step IDs provided above. Do not create new IDs.
NOTE: Only create plans for the test steps, not the assertions. Assertions will be validated separately.
`;

    try {
      const result = await generateObject({
        model: this.model,
        prompt,
        schema: executionPlanSchema,
      });

      const plannedSteps: PlannedStep[] = result.object.steps.map(step => {
        const originalStep = scenario.steps.find(s => s.id === step.stepId);
        if (!originalStep) {
          throw new Error(`Step with id ${step.stepId} not found`);
        }

        return {
          step: originalStep,
          assignedAgent: step.assignedAgent,
          dependencies: step.dependencies,
          estimatedDuration: step.estimatedDuration
        };
      });

      return {
        id: `plan_${scenario.id}_${Date.now()}`,
        scenarioId: scenario.id,
        steps: plannedSteps,
        estimatedDuration: result.object.estimatedTotalDuration,
        agents: [
          {
            id: 'executor',
            name: 'Executor Agent',
            type: 'executor',
            status: 'idle',
            capabilities: ['navigate', 'click', 'type', 'wait', 'scroll']
          },
          {
            id: 'validator',
            name: 'Validator Agent',
            type: 'validator',
            status: 'idle',
            capabilities: ['assert', 'verify', 'check_element']
          },
          {
            id: 'reporter',
            name: 'Reporter Agent',
            type: 'reporter',
            status: 'idle',
            capabilities: ['screenshot', 'extract', 'report']
          }
        ]
      };
    } catch (error) {
      console.error('Error creating execution plan:', error);
      throw new Error('Failed to create execution plan');
    }
  }

  async optimizePlan(plan: ExecutionPlan): Promise<ExecutionPlan> {
    // Add optimization logic here
    return plan;
  }
}
