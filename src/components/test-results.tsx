import { TestResult, TestScenario } from '@/types/automation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, Clock, AlertCircle } from 'lucide-react';

interface TestResultsProps {
  results: TestResult[];
  scenarios: TestScenario[];
}

export function TestResults({ results, scenarios }: TestResultsProps) {
  const getScenarioName = (scenarioId: string) => {
    const scenario = scenarios.find(s => s.id === scenarioId);
    return scenario?.name || 'Unknown Scenario';
  };

  const getStatusIcon = (status: 'passed' | 'failed') => {
    if (status === 'passed') {
      return <CheckCircle className="h-4 w-4 text-green-500" />;
    }
    return <XCircle className="h-4 w-4 text-red-500" />;
  };

  const getStatusBadge = (status: 'passed' | 'failed') => {
    return (
      <Badge variant={status === 'passed' ? 'default' : 'destructive'}>
        {status}
      </Badge>
    );
  };

  if (results.length === 0) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <div className="text-muted-foreground">
            No test results available yet. Execute some test scenarios to see results here.
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {results.map((result, index) => (
        <Card key={`${result.scenarioId}-${index}`}>
          <CardHeader>
            <div className="flex items-start justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  {getStatusIcon(result.status)}
                  {getScenarioName(result.scenarioId)}
                </CardTitle>
                <CardDescription>
                  Executed at {new Date(result.timestamp).toLocaleString()}
                </CardDescription>
              </div>
              <div className="flex items-center gap-2">
                {getStatusBadge(result.status)}
                <Badge variant="outline">
                  <Clock className="h-3 w-3 mr-1" />
                  {result.executionTime}ms
                </Badge>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Step Results */}
              <div>
                <h4 className="font-medium mb-2">Step Results:</h4>
                {result.stepResults.some(sr => sr.status === 'skipped') && (
                  <div className="mb-3 p-2 bg-yellow-50 border border-yellow-200 rounded text-sm text-yellow-800">
                    ⚠️ Some steps were skipped due to early termination after a failure
                  </div>
                )}
                <div className="space-y-2">
                  {result.stepResults.map((stepResult) => (
                    <div
                      key={stepResult.stepId}
                      className={`flex items-center justify-between p-2 rounded-lg ${
                        stepResult.status === 'passed'
                          ? 'bg-green-50 border border-green-200'
                          : stepResult.status === 'failed'
                          ? 'bg-red-50 border border-red-200'
                          : 'bg-gray-50 border border-gray-200'
                      }`}
                    >
                      <div className="flex items-center gap-2">
                        {stepResult.status === 'passed' && (
                          <CheckCircle className="h-4 w-4 text-green-500" />
                        )}
                        {stepResult.status === 'failed' && (
                          <XCircle className="h-4 w-4 text-red-500" />
                        )}
                        {stepResult.status === 'skipped' && (
                          <AlertCircle className="h-4 w-4 text-yellow-500" />
                        )}
                        <span className="text-sm font-medium">
                          Step {stepResult.stepId}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge
                          variant={
                            stepResult.status === 'passed'
                              ? 'default'
                              : stepResult.status === 'failed'
                              ? 'destructive'
                              : 'secondary'
                          }
                          className="text-xs"
                        >
                          {stepResult.status}
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          {stepResult.executionTime}ms
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Errors */}
              {result.errors.length > 0 && (
                <div>
                  <h4 className="font-medium mb-2 text-red-600">Errors:</h4>
                  <div className="space-y-1">
                    {result.errors.map((error, errorIndex) => (
                      <div
                        key={errorIndex}
                        className="p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700"
                      >
                        {error}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Screenshots */}
              {result.screenshots.length > 0 && (
                <div>
                  <h4 className="font-medium mb-2">Screenshots:</h4>
                  <div className="text-sm text-muted-foreground">
                    {result.screenshots.length} screenshot(s) captured during execution
                  </div>
                </div>
              )}

              {/* Summary */}
              <div className="flex justify-between items-center pt-2 border-t">
                <div className="text-sm text-muted-foreground">
                  {result.stepResults.filter(sr => sr.status === 'passed').length} passed,{' '}
                  {result.stepResults.filter(sr => sr.status === 'failed').length} failed,{' '}
                  {result.stepResults.filter(sr => sr.status === 'skipped').length} skipped
                  {' '}of {result.stepResults.length} steps
                </div>
                <div className="text-sm font-medium">
                  Total execution time: {result.executionTime}ms
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
