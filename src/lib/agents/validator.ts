import { generateObject } from 'ai';
import { createOpenAI } from '@ai-sdk/openai';
import { z } from 'zod';
import { Assertion, WebElementInfo } from '@/types/automation';

const validationResultSchema = z.object({
  passed: z.boolean(),
  message: z.string(),
  actualValue: z.string().optional(),
  screenshot: z.string().optional(),
  reasoning: z.string()
});

export class ValidatorAgent {
  private ollama = createOpenAI({
    baseURL: process.env.OLLAMA_BASE_URL + '/v1',
    apiKey: 'not-needed'
  });
  private model = this.ollama(process.env.OLLAMA_MODEL_ID!);

  async validateAssertion(assertion: Assertion, pageContext?: string): Promise<{
    passed: boolean;
    message: string;
    actualValue?: string;
    screenshot?: string;
  }> {
    const prompt = `
You are a web automation validator. Validate the following assertion:

Assertion Type: ${assertion.type}
Description: ${assertion.description}
${assertion.selector ? `Selector: ${assertion.selector}` : ''}
${assertion.expectedValue ? `Expected Value: ${assertion.expectedValue}` : ''}

${pageContext ? `Page Context: ${pageContext}` : ''}

Based on the assertion type, determine if the assertion passes or fails:

1. exists: Check if element exists in the DOM
2. not_exists: Check if element does NOT exist in the DOM
3. contains_text: Check if element contains specific text
4. equals: Check if element text/value equals expected value
5. visible: Check if element is visible to the user
6. not_visible: Check if element is NOT visible to the user

Provide detailed reasoning for your validation decision.
`;

    try {
      const result = await generateObject({
        model: this.model,
        prompt,
        schema: validationResultSchema,
      });

      return {
        passed: result.object.passed,
        message: result.object.message,
        actualValue: result.object.actualValue,
        screenshot: result.object.screenshot
      };
    } catch (error) {
      console.error('Error validating assertion:', error);
      return {
        passed: false,
        message: 'Validation failed due to internal error',
      };
    }
  }

  async validateMultipleAssertions(assertions: Assertion[]): Promise<{
    allPassed: boolean;
    results: Array<{
      assertionId: string;
      passed: boolean;
      message: string;
    }>;
  }> {
    const results = await Promise.all(
      assertions.map(async (assertion) => {
        const validationResult = await this.validateAssertion(assertion);
        return {
          assertionId: assertion.id,
          passed: validationResult.passed,
          message: validationResult.message
        };
      })
    );

    return {
      allPassed: results.every(r => r.passed),
      results
    };
  }

  async validateElementState(selector: string, expectedState: {
    exists?: boolean;
    visible?: boolean;
    text?: string;
    value?: string;
  }): Promise<boolean> {
    // In a real implementation, this would interact with the browser
    console.log(`Validating element state for: ${selector}`, expectedState);
    
    // Simulate validation logic
    return Math.random() > 0.2; // 80% success rate for demo
  }

  async getElementInfo(selector: string): Promise<WebElementInfo | null> {
    // In a real implementation, this would get actual element information from the browser
    console.log(`Getting element info for: ${selector}`);
    
    return {
      selector,
      tagName: 'div',
      text: 'Sample element text',
      attributes: {
        'class': 'sample-class',
        'id': 'sample-id'
      },
      isVisible: true,
      boundingBox: {
        x: 100,
        y: 200,
        width: 300,
        height: 50
      }
    };
  }

  async waitForElement(selector: string, timeout: number = 5000): Promise<boolean> {
    // In a real implementation, this would wait for element to appear
    console.log(`Waiting for element: ${selector} (timeout: ${timeout}ms)`);
    
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(Math.random() > 0.1); // 90% success rate for demo
      }, Math.min(timeout, 1000)); // Simulate up to 1 second wait
    });
  }

  async validatePageLoad(url: string, expectedElements: string[]): Promise<{
    loaded: boolean;
    missingElements: string[];
  }> {
    console.log(`Validating page load for: ${url}`);
    
    // Simulate checking for expected elements
    const missingElements = expectedElements.filter(() => Math.random() < 0.1); // 10% chance each element is missing
    
    return {
      loaded: missingElements.length === 0,
      missingElements
    };
  }
}
