import { generateText, tool } from 'ai';
import { createOpenAI } from '@ai-sdk/openai';
import { z } from 'zod';
import { TestStep, StepResult } from '@/types/automation';
import { chromium, <PERSON>rowser, B<PERSON>er<PERSON>ontext, Page } from 'playwright';

export class ExecutorAgent {
  private ollama = createOpenAI({
    baseURL: process.env.OLLAMA_BASE_URL + '/v1',
    apiKey: 'not-needed'
  });
  private model = this.ollama(process.env.OLLAMA_MODEL_ID!);
  private browser: Browser | null = null;
  private context: BrowserContext | null = null;
  private page: Page | null = null;

  async initialize() {
    if (!this.browser) {
      this.browser = await chromium.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
      });
      this.context = await this.browser.newContext({
        viewport: { width: 1280, height: 720 },
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
      });
      this.page = await this.context.newPage();
    }
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
      this.context = null;
      this.page = null;
    }
  }

  async executeStep(step: TestStep): Promise<StepResult> {
    const startTime = Date.now();
    
    try {
      await this.initialize();
      const result = await this.performAction(step);
      const executionTime = Date.now() - startTime;

      return {
        stepId: step.id,
        status: 'passed',
        executionTime,
        screenshot: result.screenshot
      };
    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      return {
        stepId: step.id,
        status: 'failed',
        executionTime,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private async performAction(step: TestStep): Promise<{ screenshot?: string }> {
    if (!this.page) {
      throw new Error('Browser page not initialized');
    }

    const tools = {
      navigate: tool({
        description: 'Navigate to a URL using Playwright',
        parameters: z.object({
          url: z.string().describe('The URL to navigate to'),
          waitUntil: z.enum(['load', 'domcontentloaded', 'networkidle']).optional().describe('When to consider navigation complete'),
        }),
        execute: async ({ url, waitUntil = 'load' }) => {
          if (!this.page) throw new Error('Browser page not initialized');
          
          console.log(`Navigating to: ${url}`);
          await this.page.goto(url, { 
            waitUntil, 
            timeout: step.timeout || 30000 
          });
          
          const title = await this.page.title();
          const currentUrl = this.page.url();
          
          return { 
            success: true, 
            message: `Successfully navigated to ${url}`,
            title,
            currentUrl
          };
        },
      }),

      click: tool({
        description: 'Click on an element using Playwright',
        parameters: z.object({
          selector: z.string().describe('CSS selector for the element to click'),
          timeout: z.number().optional().describe('Timeout in milliseconds'),
          force: z.boolean().optional().describe('Whether to force the click'),
        }),
        execute: async ({ selector, timeout = step.timeout || 10000, force = false }) => {
          if (!this.page) throw new Error('Browser page not initialized');
          
          console.log(`Clicking element: ${selector}`);
          const element = this.page.locator(selector);
          
          await element.waitFor({ state: 'visible', timeout });
          await element.click({ force, timeout });
          
          return { 
            success: true, 
            message: `Successfully clicked ${selector}` 
          };
        },
      }),

      type: tool({
        description: 'Type text into an input field using Playwright',
        parameters: z.object({
          selector: z.string().describe('CSS selector for the input field'),
          text: z.string().describe('Text to type'),
          clear: z.boolean().optional().describe('Whether to clear the field first'),
          delay: z.number().optional().describe('Delay between keystrokes in milliseconds'),
        }),
        execute: async ({ selector, text, clear = true, delay = 0 }) => {
          if (!this.page) throw new Error('Browser page not initialized');
          
          console.log(`Typing "${text}" into ${selector}`);
          const element = this.page.locator(selector);
          
          await element.waitFor({ state: 'visible', timeout: step.timeout || 10000 });
          
          if (clear) {
            await element.clear();
          }
          
          await element.type(text, { delay });
          
          return { 
            success: true, 
            message: `Successfully typed text into ${selector}` 
          };
        },
      }),

      wait: tool({
        description: 'Wait for a specified duration or for an element to appear using Playwright',
        parameters: z.object({
          duration: z.number().optional().describe('Duration to wait in milliseconds'),
          selector: z.string().optional().describe('CSS selector to wait for'),
          state: z.enum(['attached', 'detached', 'visible', 'hidden']).optional().describe('Element state to wait for'),
        }),
        execute: async ({ duration, selector, state = 'visible' }) => {
          if (!this.page) throw new Error('Browser page not initialized');
          
          if (duration) {
            console.log(`Waiting for ${duration}ms`);
            await this.page.waitForTimeout(duration);
          }
          
          if (selector) {
            console.log(`Waiting for element: ${selector} (state: ${state})`);
            await this.page.locator(selector).waitFor({ 
              state, 
              timeout: step.timeout || 10000 
            });
          }
          
          return { success: true, message: 'Wait completed' };
        },
      }),

      scroll: tool({
        description: 'Scroll the page using Playwright',
        parameters: z.object({
          direction: z.enum(['up', 'down', 'left', 'right']).describe('Direction to scroll'),
          pixels: z.number().optional().describe('Number of pixels to scroll'),
          selector: z.string().optional().describe('Element to scroll into view'),
        }),
        execute: async ({ direction, pixels = 100, selector }) => {
          if (!this.page) throw new Error('Browser page not initialized');
          
          if (selector) {
            console.log(`Scrolling element ${selector} into view`);
            await this.page.locator(selector).scrollIntoViewIfNeeded();
          } else {
            console.log(`Scrolling ${direction} by ${pixels} pixels`);
            const scrollOptions = {
              up: { x: 0, y: -pixels },
              down: { x: 0, y: pixels },
              left: { x: -pixels, y: 0 },
              right: { x: pixels, y: 0 }
            };
            
            await this.page.mouse.wheel(scrollOptions[direction].x, scrollOptions[direction].y);
          }
          
          return { 
            success: true, 
            message: selector 
              ? `Scrolled element ${selector} into view`
              : `Scrolled ${direction} by ${pixels} pixels` 
          };
        },
      }),

      screenshot: tool({
        description: 'Take a screenshot of the current page using Playwright',
        parameters: z.object({
          fullPage: z.boolean().optional().describe('Whether to capture the full page'),
          selector: z.string().optional().describe('Element to screenshot'),
        }),
        execute: async ({ fullPage = false, selector }) => {
          if (!this.page) throw new Error('Browser page not initialized');
          
          console.log('Taking screenshot');
          
          let screenshot: Buffer;
          if (selector) {
            const element = this.page.locator(selector);
            await element.waitFor({ state: 'visible', timeout: step.timeout || 10000 });
            screenshot = await element.screenshot();
          } else {
            screenshot = await this.page.screenshot({ fullPage });
          }
          
          const base64Screenshot = `data:image/png;base64,${screenshot.toString('base64')}`;
          
          return { 
            success: true, 
            screenshot: base64Screenshot,
            message: 'Screenshot captured successfully'
          };
        },
      }),

      getText: tool({
        description: 'Get text content from an element using Playwright',
        parameters: z.object({
          selector: z.string().describe('CSS selector for the element'),
        }),
        execute: async ({ selector }) => {
          if (!this.page) throw new Error('Browser page not initialized');
          
          console.log(`Getting text from element: ${selector}`);
          const element = this.page.locator(selector);
          
          await element.waitFor({ state: 'visible', timeout: step.timeout || 10000 });
          const text = await element.textContent();
          
          return { 
            success: true, 
            text: text || '',
            message: `Successfully retrieved text from ${selector}` 
          };
        },
      }),

      getAttribute: tool({
        description: 'Get attribute value from an element using Playwright',
        parameters: z.object({
          selector: z.string().describe('CSS selector for the element'),
          attribute: z.string().describe('Attribute name to get'),
        }),
        execute: async ({ selector, attribute }) => {
          if (!this.page) throw new Error('Browser page not initialized');
          
          console.log(`Getting attribute ${attribute} from element: ${selector}`);
          const element = this.page.locator(selector);
          
          await element.waitFor({ state: 'attached', timeout: step.timeout || 10000 });
          const value = await element.getAttribute(attribute);
          
          return { 
            success: true, 
            value: value || '',
            message: `Successfully retrieved ${attribute} from ${selector}` 
          };
        },
      }),

      hover: tool({
        description: 'Hover over an element using Playwright',
        parameters: z.object({
          selector: z.string().describe('CSS selector for the element to hover'),
        }),
        execute: async ({ selector }) => {
          if (!this.page) throw new Error('Browser page not initialized');
          
          console.log(`Hovering over element: ${selector}`);
          const element = this.page.locator(selector);
          
          await element.waitFor({ state: 'visible', timeout: step.timeout || 10000 });
          await element.hover();
          
          return { 
            success: true, 
            message: `Successfully hovered over ${selector}` 
          };
        },
      }),

      selectOption: tool({
        description: 'Select an option from a dropdown using Playwright',
        parameters: z.object({
          selector: z.string().describe('CSS selector for the select element'),
          value: z.string().describe('Value to select'),
        }),
        execute: async ({ selector, value }) => {
          if (!this.page) throw new Error('Browser page not initialized');
          
          console.log(`Selecting option "${value}" from ${selector}`);
          const element = this.page.locator(selector);
          
          await element.waitFor({ state: 'visible', timeout: step.timeout || 10000 });
          await element.selectOption(value);
          
          return { 
            success: true, 
            message: `Successfully selected "${value}" from ${selector}` 
          };
        },
      }),

      evaluateJS: tool({
        description: 'Execute JavaScript code in the browser context using Playwright',
        parameters: z.object({
          code: z.string().describe('JavaScript code to execute'),
        }),
        execute: async ({ code }) => {
          if (!this.page) throw new Error('Browser page not initialized');
          
          console.log(`Executing JavaScript: ${code}`);
          const result = await this.page.evaluate((jsCode) => {
            return eval(jsCode);
          }, code);
          
          return { 
            success: true, 
            result,
            message: 'JavaScript executed successfully' 
          };
        },
      }),
    };

    const prompt = `
Execute the following web automation step using Playwright:

Type: ${step.type}
Description: ${step.description}
${step.selector ? `Selector: ${step.selector}` : ''}
${step.value ? `Value: ${step.value}` : ''}
${step.timeout ? `Timeout: ${step.timeout}ms` : ''}

Available Playwright tools:
- navigate: Navigate to URLs with wait conditions
- click: Click elements with selectors
- type: Type text into input fields
- wait: Wait for duration or elements
- scroll: Scroll page or elements
- screenshot: Capture screenshots
- getText: Get element text content
- getAttribute: Get element attributes
- hover: Hover over elements
- selectOption: Select dropdown options
- evaluateJS: Execute JavaScript in browser

Use the appropriate tool to perform this action. Always take a screenshot after significant actions for verification.
`;

    const result = await generateText({
      model: this.model,
      prompt,
      tools,
      maxSteps: 5,
    });

    // Extract screenshot from tool results if available
    const screenshotResult = result.toolResults?.find(r => 
      r.toolName === 'screenshot' && r.result && typeof r.result === 'object' && 'screenshot' in r.result
    );
    const screenshot = screenshotResult?.result && typeof screenshotResult.result === 'object' && 'screenshot' in screenshotResult.result 
      ? (screenshotResult.result as { screenshot: string }).screenshot 
      : undefined;

    return { screenshot };
  }

  async validateElement(selector: string): Promise<boolean> {
    if (!this.page) {
      await this.initialize();
    }
    
    try {
      const element = this.page!.locator(selector);
      await element.waitFor({ state: 'attached', timeout: 5000 });
      return true;
    } catch (error) {
      console.log(`Element validation failed for ${selector}:`, error);
      return false;
    }
  }

  async getElementInfo(selector: string): Promise<{
    selector: string;
    exists: boolean;
    visible: boolean;
    text: string;
    tagName: string;
  }> {
    if (!this.page) {
      await this.initialize();
    }
    
    try {
      const element = this.page!.locator(selector);
      
      const exists = await element.count() > 0;
      if (!exists) {
        return {
          selector,
          exists: false,
          visible: false,
          text: '',
          tagName: ''
        };
      }
      
      const visible = await element.isVisible();
      const text = await element.textContent() || '';
      const tagName = await element.evaluate(el => el.tagName.toLowerCase());
      
      return {
        selector,
        exists,
        visible,
        text,
        tagName
      };
    } catch (error) {
      console.log(`Error getting element info for ${selector}:`, error);
      return {
        selector,
        exists: false,
        visible: false,
        text: '',
        tagName: ''
      };
    }
  }
}
