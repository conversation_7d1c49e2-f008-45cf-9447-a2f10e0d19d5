import { NextRequest, NextResponse } from 'next/server';
import { TestOrchestrator } from '@/lib/orchestrator';
import { TestScenario } from '@/types/automation';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { scenario } = body as { scenario: TestScenario };

    if (!scenario) {
      return NextResponse.json(
        { error: 'Test scenario is required' },
        { status: 400 }
      );
    }

    const orchestrator = new TestOrchestrator();
    const result = await orchestrator.executeScenario(scenario);

    return NextResponse.json({
      success: true,
      result
    });

  } catch (error) {
    console.error('Error executing test scenario:', error);
    return NextResponse.json(
      { 
        error: 'Failed to execute test scenario',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Test execution endpoint',
    usage: 'POST with test scenario data'
  });
}
