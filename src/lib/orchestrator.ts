import { PlannerAgent } from './agents/planner';
import { ExecutorAgent } from './agents/executor';
import { ValidatorAgent } from './agents/validator';
import { ReporterAgent } from './agents/reporter';
import { TestScenario, TestResult, ExecutionPlan, Agent, StepResult } from '@/types/automation';

interface ValidationResult {
  assertionId: string;
  passed: boolean;
  message: string;
}

export class TestOrchestrator {
  private plannerAgent: PlannerAgent;
  private executorAgent: ExecutorAgent;
  private validatorAgent: ValidatorAgent;
  private reporterAgent: ReporterAgent;
  private activeAgents: Map<string, Agent> = new Map();

  constructor() {
    this.plannerAgent = new PlannerAgent();
    this.executorAgent = new ExecutorAgent();
    this.validatorAgent = new ValidatorAgent();
    this.reporterAgent = new ReporterAgent();

    // Initialize agent registry
    this.initializeAgents();
  }

  private initializeAgents(): void {
    const agents: Agent[] = [
      {
        id: 'planner',
        name: 'Planner Agent',
        type: 'planner',
        status: 'idle',
        capabilities: ['analyze', 'plan', 'optimize']
      },
      {
        id: 'executor',
        name: 'Executor Agent',
        type: 'executor',
        status: 'idle',
        capabilities: ['navigate', 'click', 'type', 'wait', 'scroll']
      },
      {
        id: 'validator',
        name: 'Validator Agent',
        type: 'validator',
        status: 'idle',
        capabilities: ['assert', 'verify', 'validate']
      },
      {
        id: 'reporter',
        name: 'Reporter Agent',
        type: 'reporter',
        status: 'idle',
        capabilities: ['report', 'analyze', 'screenshot']
      }
    ];

    agents.forEach(agent => {
      this.activeAgents.set(agent.id, agent);
    });
  }

  async executeScenario(scenario: TestScenario): Promise<TestResult> {
    console.log(`Starting execution of scenario: ${scenario.name}`);
    
    try {
      // Update scenario status
      scenario.status = 'running';
      
      // Phase 1: Planning
      this.updateAgentStatus('planner', 'active');
      console.log('Phase 1: Creating execution plan...');
      const executionPlan = await this.plannerAgent.createExecutionPlan(scenario);
      this.updateAgentStatus('planner', 'idle');
      
      // Phase 2: Execution
      this.updateAgentStatus('executor', 'active');
      console.log('Phase 2: Executing test steps...');
      const stepResults = await this.executeSteps(executionPlan);
      this.updateAgentStatus('executor', 'idle');
      
      // Check if any steps failed - if so, skip validation phase
      const hasFailedSteps = stepResults.some(sr => sr.status === 'failed');
      let validationResults: ValidationResult[] = [];
      
      if (hasFailedSteps) {
        console.log('Skipping validation phase due to step failures - test case has already failed');
      } else {
        // Phase 3: Validation
        this.updateAgentStatus('validator', 'active');
        console.log('Phase 3: Validating assertions...');
        validationResults = await this.validateAssertions(scenario);
        this.updateAgentStatus('validator', 'idle');
      }
      
      // Phase 4: Reporting
      this.updateAgentStatus('reporter', 'active');
      console.log('Phase 4: Generating report...');
      const screenshots = await this.captureScreenshots();
      this.updateAgentStatus('reporter', 'idle');
      
      // Compile results
      const testResult: TestResult = {
        scenarioId: scenario.id,
        status: this.determineOverallStatus(stepResults, validationResults),
        executionTime: stepResults.reduce((sum, sr) => sum + sr.executionTime, 0),
        stepResults,
        errors: this.collectErrors(stepResults, validationResults),
        screenshots,
        timestamp: new Date()
      };
      
      // Update scenario status
      scenario.status = testResult.status === 'passed' ? 'passed' : 'failed';
      
      console.log(`Scenario execution completed: ${testResult.status}`);
      return testResult;
      
    } catch (error) {
      console.error('Error executing scenario:', error);
      scenario.status = 'failed';
      
      return {
        scenarioId: scenario.id,
        status: 'failed',
        executionTime: 0,
        stepResults: [],
        errors: [error instanceof Error ? error.message : 'Unknown error'],
        screenshots: [],
        timestamp: new Date()
      };
    }
  }

  async executeMultipleScenarios(scenarios: TestScenario[]): Promise<TestResult[]> {
    console.log(`Executing ${scenarios.length} scenarios...`);
    
    const results: TestResult[] = [];
    
    for (const scenario of scenarios) {
      const result = await this.executeScenario(scenario);
      results.push(result);
      
      // Stop executing remaining scenarios if current one failed
      if (result.status === 'failed') {
        console.log(`Scenario '${scenario.name}' failed. Stopping execution of remaining scenarios.`);
        break;
      }
    }
    
    // Generate comprehensive report
    await this.generateComprehensiveReport(scenarios, results);
    
    return results;
  }

  private async executeSteps(executionPlan: ExecutionPlan) {
    const stepResults = [];
    let executionStopped = false;
    
    for (let i = 0; i < executionPlan.steps.length; i++) {
      const plannedStep = executionPlan.steps[i];
      
      if (executionStopped) {
        // Mark remaining steps as skipped
        stepResults.push({
          stepId: plannedStep.step.id,
          status: 'skipped' as const,
          executionTime: 0,
          error: 'Skipped due to previous step failure'
        });
        continue;
      }
      
      console.log(`Executing step: ${plannedStep.step.description}`);
      
      try {
        const result = await this.executorAgent.executeStep(plannedStep.step);
        stepResults.push(result);
        
        if (result.status === 'failed') {
          console.warn(`Step failed: ${result.error}`);
          console.log('Stopping execution due to step failure - no need to proceed');
          executionStopped = true;
        }
      } catch (error) {
        console.error(`Error executing step: ${error}`);
        const failedResult = {
          stepId: plannedStep.step.id,
          status: 'failed' as const,
          executionTime: 0,
          error: error instanceof Error ? error.message : 'Unknown error'
        };
        stepResults.push(failedResult);
        console.log('Stopping execution due to step execution error - no need to proceed');
        executionStopped = true;
      }
    }
    
    return stepResults;
  }

  private async validateAssertions(scenario: TestScenario) {
    const validationResults = [];
    let validationStopped = false;
    
    for (let i = 0; i < scenario.assertions.length; i++) {
      const assertion = scenario.assertions[i];
      
      if (validationStopped) {
        // Mark remaining assertions as skipped
        validationResults.push({
          assertionId: assertion.id,
          passed: false,
          message: 'Skipped due to previous assertion failure'
        });
        continue;
      }
      
      console.log(`Validating assertion: ${assertion.description}`);
      
      try {
        const result = await this.validatorAgent.validateAssertion(assertion);
        validationResults.push({
          assertionId: assertion.id,
          passed: result.passed,
          message: result.message
        });
        
        if (!result.passed) {
          console.warn(`Assertion failed: ${result.message}`);
          console.log('Stopping validation due to assertion failure - no need to proceed');
          validationStopped = true;
        }
      } catch (error) {
        console.error(`Error validating assertion: ${error}`);
        const failedValidation = {
          assertionId: assertion.id,
          passed: false,
          message: error instanceof Error ? error.message : 'Unknown error'
        };
        validationResults.push(failedValidation);
        console.log('Stopping validation due to assertion validation error - no need to proceed');
        validationStopped = true;
      }
    }
    
    return validationResults;
  }

  private async captureScreenshots(): Promise<string[]> {
    try {
      const screenshot = await this.reporterAgent.takeScreenshot();
      return [screenshot];
    } catch (error) {
      console.error('Error capturing screenshots:', error);
      return [];
    }
  }

  private determineOverallStatus(stepResults: StepResult[], validationResults: ValidationResult[]): 'passed' | 'failed' {
    const hasFailedSteps = stepResults.some(sr => sr.status === 'failed');
    const hasFailedValidations = validationResults.some(vr => !vr.passed);
    
    return hasFailedSteps || hasFailedValidations ? 'failed' : 'passed';
  }

  private collectErrors(stepResults: StepResult[], validationResults: ValidationResult[]): string[] {
    const errors: string[] = [];
    
    stepResults.forEach(sr => {
      if (sr.status === 'failed' && sr.error) {
        errors.push(`Step ${sr.stepId}: ${sr.error}`);
      }
    });
    
    validationResults.forEach(vr => {
      if (!vr.passed) {
        errors.push(`Assertion ${vr.assertionId}: ${vr.message}`);
      }
    });
    
    return errors;
  }

  private async generateComprehensiveReport(scenarios: TestScenario[], results: TestResult[]) {
    try {
      console.log('Generating comprehensive test report...');
      const report = await this.reporterAgent.generateReport(scenarios, results);
      console.log('Report generated successfully');
      return report;
    } catch (error) {
      console.error('Error generating report:', error);
    }
  }

  private updateAgentStatus(agentId: string, status: Agent['status']): void {
    const agent = this.activeAgents.get(agentId);
    if (agent) {
      agent.status = status;
      this.activeAgents.set(agentId, agent);
    }
  }

  getAgentStatuses(): Agent[] {
    return Array.from(this.activeAgents.values());
  }

  async generateTestScenarioFromDescription(description: string, url: string): Promise<TestScenario> {
    // This could use AI to generate test scenarios from natural language descriptions
    console.log(`Generating test scenario for: ${description}`);
    
    // For now, return a basic scenario structure
    return {
      id: `scenario_${Date.now()}`,
      name: 'AI Generated Scenario',
      description,
      url,
      steps: [],
      assertions: [],
      status: 'pending',
      createdAt: new Date(),
      updatedAt: new Date()
    };
  }
}
